import mysql from 'mysql2/promise';
import bcrypt from 'bcrypt';
import dotenv from 'dotenv';

dotenv.config();

// 创建数据库连接池
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root',
  database: process.env.DB_NAME || 'dorm_management',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  charset: 'utf8mb4'
});

// 测试数据库连接并自动创建数据库
const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    console.log('✅ 数据库连接成功');

    // 检查表是否存在
    try {
      await connection.execute('SELECT 1 FROM users LIMIT 1');
      console.log('✅ 数据库表已存在');

      // 检查公告表是否存在
      try {
        await connection.execute('SELECT 1 FROM announcements LIMIT 1');
        console.log('✅ 公告表已存在');
      } catch (announcementError) {
        if (announcementError.code === 'ER_NO_SUCH_TABLE') {
          console.log('⚠️ 公告表不存在，正在创建...');
          await createAnnouncementsTable(connection);
        }
      }
    } catch (tableError) {
      if (tableError.code === 'ER_NO_SUCH_TABLE') {
        console.log('⚠️ 数据库表不存在，正在创建...');
        await createTablesAndData(connection);
      } else {
        throw tableError;
      }
    }

    connection.release();
  } catch (error) {
    if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('⚠️ 数据库不存在，正在创建...');
      await createDatabaseAndTables();
    } else {
      console.error('❌ 数据库连接失败:', error.message);
      process.exit(1);
    }
  }
};

// 创建表和数据（使用现有连接）
const createTablesAndData = async (connection) => {
  try {
    console.log('🔄 开始创建表...');

    // 删除现有表（如果存在）
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0');
    await connection.execute('DROP TABLE IF EXISTS repair_requests');
    await connection.execute('DROP TABLE IF EXISTS users');
    await connection.execute('DROP TABLE IF EXISTS majors');
    await connection.execute('DROP TABLE IF EXISTS colleges');
    await connection.execute('DROP TABLE IF EXISTS dorm_buildings');
    await connection.execute('DROP TABLE IF EXISTS user_roles');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1');

    // 创建用户角色表
    await connection.execute(`
      CREATE TABLE user_roles (
        id INT PRIMARY KEY AUTO_INCREMENT,
        role_name VARCHAR(50) NOT NULL UNIQUE,
        role_description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 创建学院表
    await connection.execute(`
      CREATE TABLE colleges (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 创建专业表
    await connection.execute(`
      CREATE TABLE majors (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        college_id VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (college_id) REFERENCES colleges(id)
      )
    `);

    // 创建宿舍楼表
    await connection.execute(`
      CREATE TABLE dorm_buildings (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        floors INT NOT NULL,
        total_rooms INT NOT NULL,
        assigned_admin_id VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 创建用户表
    await connection.execute(`
      CREATE TABLE users (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(150) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        role_id INT NOT NULL,
        college_id VARCHAR(50),
        major_id VARCHAR(50),
        dorm_building_id VARCHAR(50),
        room_number VARCHAR(20),
        emergency_contact_name VARCHAR(100),
        emergency_contact_phone VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (role_id) REFERENCES user_roles(id),
        FOREIGN KEY (college_id) REFERENCES colleges(id),
        FOREIGN KEY (major_id) REFERENCES majors(id),
        FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id)
      )
    `);

    // 创建维修请求表
    await connection.execute(`
      CREATE TABLE repair_requests (
        id VARCHAR(50) PRIMARY KEY,
        student_id VARCHAR(50) NOT NULL,
        room_number VARCHAR(20),
        dorm_building_id VARCHAR(50),
        description TEXT NOT NULL,
        status ENUM('待处理', '已指派', '维修中', '已完成', '已确认', '已取消') DEFAULT '待处理',
        assigned_staff_id VARCHAR(50),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES users(id),
        FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id),
        FOREIGN KEY (assigned_staff_id) REFERENCES users(id)
      )
    `);

    console.log('✅ 表创建完成');

    // 插入基础数据
    await insertBasicData(connection);

    console.log('✅ 表和数据创建成功');

  } catch (error) {
    console.error('❌ 创建表失败:', error.message);
    throw error;
  }
};

// 创建数据库和表
const createDatabaseAndTables = async () => {
  let connection;
  try {
    // 连接到MySQL服务器（不指定数据库）
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      charset: 'utf8mb4'
    });

    // 创建数据库
    await connection.execute(`CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME || 'dorm_management'} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    await connection.execute(`USE ${process.env.DB_NAME || 'dorm_management'}`);

    // 创建表和插入数据的SQL
    const setupSQL = `
      -- 创建用户角色表
      CREATE TABLE IF NOT EXISTS user_roles (
        id INT PRIMARY KEY AUTO_INCREMENT,
        role_name VARCHAR(50) NOT NULL UNIQUE,
        role_description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      -- 创建学院表
      CREATE TABLE IF NOT EXISTS colleges (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      -- 创建专业表
      CREATE TABLE IF NOT EXISTS majors (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        college_id VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (college_id) REFERENCES colleges(id)
      );

      -- 创建宿舍楼表
      CREATE TABLE IF NOT EXISTS dorm_buildings (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        floors INT NOT NULL,
        total_rooms INT NOT NULL,
        assigned_admin_id VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      -- 创建用户表
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(150) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        role_id INT NOT NULL,
        college_id VARCHAR(50),
        major_id VARCHAR(50),
        dorm_building_id VARCHAR(50),
        room_number VARCHAR(20),
        emergency_contact_name VARCHAR(100),
        emergency_contact_phone VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (role_id) REFERENCES user_roles(id),
        FOREIGN KEY (college_id) REFERENCES colleges(id),
        FOREIGN KEY (major_id) REFERENCES majors(id),
        FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id)
      );

      -- 创建维修请求表
      CREATE TABLE IF NOT EXISTS repair_requests (
        id VARCHAR(50) PRIMARY KEY,
        student_id VARCHAR(50) NOT NULL,
        room_number VARCHAR(20),
        dorm_building_id VARCHAR(50),
        description TEXT NOT NULL,
        status ENUM('待处理', '已指派', '维修中', '已完成', '已确认', '已取消') DEFAULT '待处理',
        assigned_staff_id VARCHAR(50),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES users(id),
        FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id),
        FOREIGN KEY (assigned_staff_id) REFERENCES users(id)
      );
    `;

    // 分割并执行SQL语句
    const statements = setupSQL.split(';').filter(stmt => stmt.trim().length > 0);
    for (const statement of statements) {
      if (statement.trim()) {
        await connection.execute(statement);
      }
    }

    // 插入基础数据
    await insertBasicData(connection);

    console.log('✅ 数据库和表创建成功');
    await connection.end();

    // 重新测试连接
    const newConnection = await pool.getConnection();
    console.log('✅ 数据库连接成功');
    newConnection.release();

  } catch (error) {
    console.error('❌ 创建数据库失败:', error.message);
    if (connection) await connection.end();
    process.exit(1);
  }
};

// 创建公告表
const createAnnouncementsTable = async (connection) => {
  try {
    // 创建公告表
    await connection.execute(`
      CREATE TABLE announcements (
        id VARCHAR(50) PRIMARY KEY,
        title VARCHAR(200) NOT NULL,
        content TEXT NOT NULL,
        author_id VARCHAR(50) NOT NULL,
        scope ENUM('All', 'DormBuilding', 'College') DEFAULT 'All',
        target_id VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);

    // 插入测试公告数据
    await connection.execute(`
      INSERT INTO announcements (id, title, content, author_id, scope, target_id) VALUES
      ('anno001', '近期维修安排通知', '请注意，A栋将于7月25日上午9点至下午5点进行计划内维修。维修期间可能会影响正常用水用电，请提前做好准备。如有紧急情况，请联系宿舍管理员。', 'sysadmin01', 'All', NULL),
      ('anno002', '宿舍会议 - B栋', 'B栋全体住宿学生务必参加7月26日晚上7点在公共活动室举行的会议。会议将讨论宿舍管理规定和安全事项，请准时参加。', 'dormadmin01', 'DormBuilding', 'bldgB'),
      ('anno003', '计算机科学学院通知', '计算机科学学院学生请注意：下周将进行宿舍安全检查，请保持宿舍整洁。', 'sysadmin01', 'College', 'college01')
    `);

    console.log('✅ 公告表创建成功');
  } catch (error) {
    console.error('❌ 创建公告表失败:', error.message);
    throw error;
  }
};

// 插入基础数据
const insertBasicData = async (connection) => {
  try {
    // 插入角色数据
    await connection.execute(`
      INSERT INTO user_roles (id, role_name, role_description) VALUES
      (1, '系统管理员', '系统管理员，拥有所有权限'),
      (2, '宿舍管理员', '宿舍管理员，管理特定宿舍楼'),
      (3, '学生', '学生用户'),
      (4, '维修人员', '维修人员，处理维修请求')
    `);

    // 插入学院数据
    await connection.execute(`
      INSERT INTO colleges (id, name) VALUES
      ('college01', '计算机科学学院'),
      ('college02', '电子工程学院'),
      ('college03', '机械工程学院'),
      ('college04', '经济管理学院')
    `);

    // 插入专业数据
    await connection.execute(`
      INSERT INTO majors (id, name, college_id) VALUES
      ('major01', '计算机科学与技术', 'college01'),
      ('major02', '软件工程', 'college01'),
      ('major03', '电子信息工程', 'college02'),
      ('major04', '通信工程', 'college02')
    `);

    // 插入宿舍楼数据
    await connection.execute(`
      INSERT INTO dorm_buildings (id, name, floors, total_rooms) VALUES
      ('bldgA', 'A栋', 6, 120),
      ('bldgB', 'B栋', 8, 160),
      ('bldgC', 'C栋', 5, 100)
    `);

    // 生成密码哈希
    const passwordHash = await bcrypt.hash('password123', 10);

    // 插入用户数据
    await connection.execute(`
      INSERT INTO users (id, name, email, password, role_id, phone, college_id, major_id, dorm_building_id, emergency_contact_name, emergency_contact_phone) VALUES
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      'sysadmin01', '系统管理员', '<EMAIL>', passwordHash, 1, '13800000001', null, null, null, null, null,
      'repair01', '爱德华·修理工', '<EMAIL>', passwordHash, 4, '13800000005', null, null, null, null, null,
      'dormadmin01', '张三', '<EMAIL>', passwordHash, 2, '13800000002', null, null, 'bldgA', null, null,
      'dormadmin02', '李四', '<EMAIL>', passwordHash, 2, '13800000003', null, null, 'bldgB', null, null,
      'student01', '王五', '<EMAIL>', passwordHash, 3, '13800000004', 'college01', 'major01', 'bldgA', '王父', '13900000001',
      'student02', '赵六', '<EMAIL>', passwordHash, 3, '13800000006', 'college02', 'major03', 'bldgB', '赵母', '13900000002'
    ]);

    // 插入维修请求数据
    await connection.execute(`
      INSERT INTO repair_requests (id, student_id, room_number, dorm_building_id, description, status, assigned_staff_id, notes) VALUES
      ('repair001', 'student01', '101', 'bldgA', '水龙头漏水', '待处理', NULL, NULL),
      ('repair002', 'student02', '205', 'bldgB', '灯泡坏了', '已指派', 'repair01', '已安排维修人员')
    `);

    console.log('✅ 基础数据插入成功');
  } catch (error) {
    console.error('⚠️ 插入基础数据时出错:', error.message);
    throw error;
  }
};

export { pool, testConnection };
