import { pool } from '../config/database.js';

// 获取违规记录列表（支持按宿舍楼筛选）
export const getViolations = async (req, res) => {
  try {
    const { building_id } = req.query;
    
    let query = `
      SELECT v.*, u.name as student_name, u.room_number, db.name as building_name
      FROM violations v
      LEFT JOIN users u ON v.student_id = u.id
      LEFT JOIN dorm_buildings db ON v.dorm_building_id = db.id
    `;
    let params = [];

    if (building_id) {
      query += ' WHERE v.dorm_building_id = ?';
      params.push(building_id);
    }

    query += ' ORDER BY v.date DESC, v.created_at DESC';

    const [violations] = await pool.execute(query, params);

    // 调试信息
    console.log('获取违规记录查询:', query);
    console.log('查询参数:', params);
    console.log('查询结果:', violations);

    res.json({
      success: true,
      data: violations
    });

  } catch (error) {
    console.error('获取违规记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 创建违规记录
export const createViolation = async (req, res) => {
  try {
    const { 
      student_id, 
      dorm_building_id, 
      date, 
      type, 
      description, 
      action_taken 
    } = req.body;

    if (!student_id || !dorm_building_id || !date || !type || !description) {
      return res.status(400).json({
        success: false,
        message: '学生ID、宿舍楼ID、日期、违规类型和描述都是必填项'
      });
    }

    // 生成违规记录ID
    const violationId = `viol_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 调试信息
    console.log('创建违规记录参数:', {
      violationId,
      student_id,
      dorm_building_id,
      date,
      type,
      description,
      action_taken: action_taken || null,
      recorded_by: req.user?.id
    });

    // 插入新违规记录
    await pool.execute(
      `INSERT INTO violations (
        id, student_id, dorm_building_id, date, type, description, action_taken, recorded_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        violationId,
        student_id,
        dorm_building_id,
        date,
        type,
        description,
        action_taken || null,
        req.user?.id || null
      ]
    );

    res.status(201).json({
      success: true,
      message: '违规记录创建成功',
      data: {
        violationId
      }
    });

  } catch (error) {
    console.error('创建违规记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 更新违规记录
export const updateViolation = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      date, 
      type, 
      description, 
      action_taken 
    } = req.body;

    // 检查违规记录是否存在
    const [existingViolations] = await pool.execute('SELECT id FROM violations WHERE id = ?', [id]);
    if (existingViolations.length === 0) {
      return res.status(404).json({
        success: false,
        message: '违规记录不存在'
      });
    }

    // 更新违规记录
    await pool.execute(
      `UPDATE violations SET 
        date = ?, type = ?, description = ?, action_taken = ?
       WHERE id = ?`,
      [date, type, description, action_taken || null, id]
    );

    res.json({
      success: true,
      message: '违规记录更新成功'
    });

  } catch (error) {
    console.error('更新违规记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 删除违规记录
export const deleteViolation = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查违规记录是否存在
    const [existingViolations] = await pool.execute('SELECT id FROM violations WHERE id = ?', [id]);
    if (existingViolations.length === 0) {
      return res.status(404).json({
        success: false,
        message: '违规记录不存在'
      });
    }

    // 删除违规记录
    await pool.execute('DELETE FROM violations WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '违规记录删除成功'
    });

  } catch (error) {
    console.error('删除违规记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 获取违规记录详情
export const getViolationDetails = async (req, res) => {
  try {
    const { id } = req.params;

    const [violations] = await pool.execute(`
      SELECT v.*, u.name as student_name, u.room_number, db.name as building_name,
             recorder.name as recorder_name
      FROM violations v
      LEFT JOIN users u ON v.student_id = u.id
      LEFT JOIN dorm_buildings db ON v.dorm_building_id = db.id
      LEFT JOIN users recorder ON v.recorded_by = recorder.id
      WHERE v.id = ?
    `, [id]);

    if (violations.length === 0) {
      return res.status(404).json({
        success: false,
        message: '违规记录不存在'
      });
    }

    res.json({
      success: true,
      data: violations[0]
    });

  } catch (error) {
    console.error('获取违规记录详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};
