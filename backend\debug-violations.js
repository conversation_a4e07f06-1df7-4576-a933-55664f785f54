import { pool } from './config/database.js';

async function checkViolationData() {
  try {
    console.log('=== 检查违规记录数据 ===');
    const [violations] = await pool.execute('SELECT * FROM violations LIMIT 5');
    console.log('违规记录:', JSON.stringify(violations, null, 2));
    
    console.log('\n=== 检查用户数据 ===');
    const [users] = await pool.execute('SELECT id, name, email, role_name FROM users WHERE role_name = "学生" LIMIT 5');
    console.log('学生用户:', JSON.stringify(users, null, 2));
    
    console.log('\n=== 检查关联查询 ===');
    const [joined] = await pool.execute(`
      SELECT v.*, u.name as student_name, u.room_number, db.name as building_name
      FROM violations v
      LEFT JOIN users u ON v.student_id = u.id
      LEFT JOIN dorm_buildings db ON v.dorm_building_id = db.id
      LIMIT 5
    `);
    console.log('关联查询结果:', JSON.stringify(joined, null, 2));
    
    process.exit(0);
  } catch (error) {
    console.error('错误:', error);
    process.exit(1);
  }
}

checkViolationData();
