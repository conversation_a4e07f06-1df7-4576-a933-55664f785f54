import { pool } from '../config/database.js';

// 获取宿舍楼内的学生列表
export const getStudentsInBuilding = async (req, res) => {
  try {
    const { building_id } = req.query;
    
    if (!building_id) {
      return res.status(400).json({
        success: false,
        message: '宿舍楼ID是必需的'
      });
    }

    console.log('获取学生列表请求:', { building_id, user: req.user?.id });

    // 获取该宿舍楼内的所有学生
    const [students] = await pool.execute(`
      SELECT
        u.id, u.name, u.email, u.phone, u.room_number, u.dorm_building_id,
        u.emergency_contact_name, u.emergency_contact_phone,
        db.name as building_name,
        b.bed_number, b.status as bed_status
      FROM users u
      LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
      LEFT JOIN beds b ON b.student_id = u.id
      WHERE u.role_name = '学生' AND (u.dorm_building_id = ? OR u.dorm_building_id IS NULL)
      ORDER BY u.name
    `, [building_id]);

    console.log('查询结果:', students.length, '个学生');
    students.forEach(s => {
      console.log(`  - ${s.name} (${s.id}) - 宿舍楼: ${s.dorm_building_id || '未分配'} - 房间: ${s.room_number || '未分配'}`);
    });

    res.json({
      success: true,
      data: students
    });

  } catch (error) {
    console.error('获取学生列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 获取宿舍楼内的空闲床位
export const getVacantBedsInBuilding = async (req, res) => {
  try {
    const { building_id } = req.query;
    
    if (!building_id) {
      return res.status(400).json({
        success: false,
        message: '宿舍楼ID是必需的'
      });
    }

    // 获取该宿舍楼内的所有空闲床位
    const [vacantBeds] = await pool.execute(`
      SELECT 
        b.id, b.bed_number, b.status,
        r.id as room_id, r.room_number, r.floor, r.type, r.capacity
      FROM beds b
      JOIN rooms r ON b.room_id = r.id
      WHERE r.dorm_building_id = ? AND b.status = '空闲'
      ORDER BY r.floor, r.room_number, b.bed_number
    `, [building_id]);

    res.json({
      success: true,
      data: vacantBeds
    });

  } catch (error) {
    console.error('获取空闲床位错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 分配学生到床位
export const assignStudentToBed = async (req, res) => {
  try {
    const { student_id, bed_id } = req.body;

    if (!student_id || !bed_id) {
      return res.status(400).json({
        success: false,
        message: '学生ID和床位ID都是必需的'
      });
    }

    // 1. 检查床位是否空闲
    const [bedCheck] = await pool.execute(`
      SELECT b.*, r.room_number, r.dorm_building_id, r.floor
      FROM beds b
      JOIN rooms r ON b.room_id = r.id
      WHERE b.id = ? AND b.status = '空闲'
    `, [bed_id]);

    if (bedCheck.length === 0) {
      return res.status(400).json({
        success: false,
        message: '床位不存在或已被占用'
      });
    }

    const bed = bedCheck[0];

    // 2. 释放学生之前的床位（如果有）
    await pool.execute(`
      UPDATE beds SET status = '空闲', student_id = NULL
      WHERE student_id = ?
    `, [student_id]);

    // 3. 分配新床位
    await pool.execute(`
      UPDATE beds SET status = '已入住', student_id = ?
      WHERE id = ?
    `, [student_id, bed_id]);

    // 4. 更新学生的房间信息
    await pool.execute(`
      UPDATE users SET
        room_number = ?,
        dorm_building_id = ?
      WHERE id = ?
    `, [bed.room_number, bed.dorm_building_id, student_id]);

    // 5. 更新房间的已住人数
    await pool.execute(`
      UPDATE rooms SET occupied_beds = (
        SELECT COUNT(*) FROM beds WHERE room_id = ? AND status = '已入住'
      ) WHERE id = ?
    `, [bed.room_id, bed.room_id]);

    res.json({
      success: true,
      message: '学生床位分配成功'
    });

  } catch (error) {
    console.error('分配学生床位错误:', error);
    res.status(500).json({
      success: false,
      message: error.message || '服务器内部错误'
    });
  }
};

// 取消学生床位分配
export const unassignStudent = async (req, res) => {
  try {
    const { student_id } = req.params;

    if (!student_id) {
      return res.status(400).json({
        success: false,
        message: '学生ID是必需的'
      });
    }

    // 1. 获取学生当前的床位信息
    const [currentBed] = await pool.execute(`
      SELECT b.*, r.id as room_id
      FROM beds b
      JOIN rooms r ON b.room_id = r.id
      WHERE b.student_id = ?
    `, [student_id]);

    if (currentBed.length === 0) {
      return res.status(400).json({
        success: false,
        message: '学生没有分配床位'
      });
    }

    const bed = currentBed[0];

    // 2. 释放床位
    await pool.execute(`
      UPDATE beds SET status = '空闲', student_id = NULL
      WHERE student_id = ?
    `, [student_id]);

    // 3. 清除学生的房间信息
    await pool.execute(`
      UPDATE users SET
        room_number = NULL,
        dorm_building_id = NULL
      WHERE id = ?
    `, [student_id]);

    // 4. 更新房间的已住人数
    await pool.execute(`
      UPDATE rooms SET occupied_beds = (
        SELECT COUNT(*) FROM beds WHERE room_id = ? AND status = '已入住'
      ) WHERE id = ?
    `, [bed.room_id, bed.room_id]);

    res.json({
      success: true,
      message: '学生床位分配已取消'
    });

  } catch (error) {
    console.error('取消学生床位分配错误:', error);
    res.status(500).json({
      success: false,
      message: error.message || '服务器内部错误'
    });
  }
};

// 更新学生紧急联系人信息
export const updateStudentEmergencyContact = async (req, res) => {
  try {
    const { student_id } = req.params;
    const { emergency_contact_name, emergency_contact_phone } = req.body;

    await pool.execute(`
      UPDATE users SET 
        emergency_contact_name = ?, 
        emergency_contact_phone = ?
      WHERE id = ?
    `, [emergency_contact_name, emergency_contact_phone, student_id]);

    res.json({
      success: true,
      message: '学生紧急联系人信息更新成功'
    });

  } catch (error) {
    console.error('更新学生紧急联系人错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};
