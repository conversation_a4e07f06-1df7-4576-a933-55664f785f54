import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const monitorChanges = async () => {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'root',
      database: 'dorm_management',
      charset: 'utf8mb4'
    });

    console.log('🔍 开始监控数据库变化...');
    console.log('按 Ctrl+C 停止监控\n');

    let lastCounts = {};

    const checkChanges = async () => {
      try {
        // 获取各表的记录数
        const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
        const [collegeCount] = await connection.execute('SELECT COUNT(*) as count FROM colleges');
        const [majorCount] = await connection.execute('SELECT COUNT(*) as count FROM majors');
        const [buildingCount] = await connection.execute('SELECT COUNT(*) as count FROM dorm_buildings');
        const [repairCount] = await connection.execute('SELECT COUNT(*) as count FROM repair_requests');

        // 检查公告表是否存在
        let announcementCount = 0;
        try {
          const [annCount] = await connection.execute('SELECT COUNT(*) as count FROM announcements');
          announcementCount = annCount[0].count;
        } catch (error) {
          // 表不存在时忽略错误
        }

        const currentCounts = {
          users: userCount[0].count,
          colleges: collegeCount[0].count,
          majors: majorCount[0].count,
          buildings: buildingCount[0].count,
          repairs: repairCount[0].count,
          announcements: announcementCount
        };

        // 检查是否有变化
        let hasChanges = false;
        for (const [table, count] of Object.entries(currentCounts)) {
          if (lastCounts[table] !== undefined && lastCounts[table] !== count) {
            console.log(`🔄 ${new Date().toLocaleTimeString()} - ${table} 表变化: ${lastCounts[table]} → ${count}`);
            hasChanges = true;
          }
        }

        if (hasChanges) {
          console.log('\n📋 最新数据:');
          
          // 显示最新用户
          if (currentCounts.users > (lastCounts.users || 0)) {
            const [latestUsers] = await connection.execute(`
              SELECT u.name, u.email, ur.role_name, u.created_at
              FROM users u 
              LEFT JOIN user_roles ur ON u.role_id = ur.id 
              ORDER BY u.created_at DESC LIMIT 3
            `);
            
            console.log('   👥 最新用户:');
            latestUsers.forEach(user => {
              console.log(`     - ${user.name} (${user.email}) - ${user.role_name} - ${user.created_at}`);
            });
          }

          // 显示最新学院
          if (currentCounts.colleges > (lastCounts.colleges || 0)) {
            const [latestColleges] = await connection.execute(`
              SELECT name, created_at FROM colleges ORDER BY created_at DESC LIMIT 3
            `);
            
            console.log('   🏫 最新学院:');
            latestColleges.forEach(college => {
              console.log(`     - ${college.name} - ${college.created_at}`);
            });
          }

          // 显示最新专业
          if (currentCounts.majors > (lastCounts.majors || 0)) {
            const [latestMajors] = await connection.execute(`
              SELECT m.name, c.name as college_name, m.created_at
              FROM majors m 
              LEFT JOIN colleges c ON m.college_id = c.id 
              ORDER BY m.created_at DESC LIMIT 3
            `);
            
            console.log('   📚 最新专业:');
            latestMajors.forEach(major => {
              console.log(`     - ${major.name} (${major.college_name}) - ${major.created_at}`);
            });
          }

          // 显示最新宿舍楼
          if (currentCounts.buildings > (lastCounts.buildings || 0)) {
            const [latestBuildings] = await connection.execute(`
              SELECT name, floors, total_rooms, created_at FROM dorm_buildings ORDER BY created_at DESC LIMIT 3
            `);
            
            console.log('   🏢 最新宿舍楼:');
            latestBuildings.forEach(building => {
              console.log(`     - ${building.name} (${building.floors}层, ${building.total_rooms}间) - ${building.created_at}`);
            });
          }

          console.log('\n' + '='.repeat(60) + '\n');
        }

        lastCounts = currentCounts;

        // 显示当前统计（每10次检查显示一次）
        if (Math.random() < 0.1) {
          console.log(`📊 ${new Date().toLocaleTimeString()} - 当前统计: 用户:${currentCounts.users}, 学院:${currentCounts.colleges}, 专业:${currentCounts.majors}, 宿舍楼:${currentCounts.buildings}, 维修:${currentCounts.repairs}, 公告:${currentCounts.announcements}`);
        }

      } catch (error) {
        console.error('❌ 监控错误:', error.message);
      }
    };

    // 初始检查
    await checkChanges();

    // 每2秒检查一次
    const interval = setInterval(checkChanges, 2000);

    // 处理退出信号
    process.on('SIGINT', () => {
      console.log('\n🛑 停止监控...');
      clearInterval(interval);
      if (connection) {
        connection.end();
      }
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ 连接数据库失败:', error.message);
    process.exit(1);
  }
};

monitorChanges();
