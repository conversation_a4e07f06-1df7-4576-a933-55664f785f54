import React, { useState, useEffect } from 'react';
import { Room, Bed, BedStatus, RoomType } from '../../types';
import { useAuth } from '../../contexts/AuthContext';
import Table from '../../components/Table';
import Card from '../../components/Card';
import Modal from '../../components/Modal';
import Button from '../../components/Button';
import Input from '../../components/Input';

const API_BASE_URL = 'http://localhost:3002/api';

const getDefaultCapacity = (type: RoomType): number => {
    switch (type) {
        case RoomType.SINGLE: return 1;
        case RoomType.DOUBLE: return 2;
        case RoomType.QUAD: return 4;
        case RoomType.HEXA: return 6;
        default: return 4;
    }
};

const RoomManagementPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [rooms, setRooms] = useState<Room[]>([]);
  const [beds, setBeds] = useState<Bed[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [adminBuildingId, setAdminBuildingId] = useState<string>('');

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [viewingRoom, setViewingRoom] = useState<Room | null>(null);

  const [isAddRoomModalOpen, setIsAddRoomModalOpen] = useState(false);
  const [newRoom, setNewRoom] = useState<Partial<Room>>({ type: RoomType.QUAD, capacity: getDefaultCapacity(RoomType.QUAD), occupiedBeds: 0 });

  // 获取宿舍楼信息
  const fetchBuildingInfo = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/dorm-buildings`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // 找到当前用户管理的宿舍楼
          const userBuilding = data.data.find((building: any) =>
            building.assigned_admin_id === currentUser?.id
          );
          if (userBuilding) {
            setAdminBuildingId(userBuilding.id);
          }
        }
      }
    } catch (error) {
      console.error('获取宿舍楼信息错误:', error);
    }
  };

  // 获取房间列表
  const fetchRooms = async () => {
    if (!adminBuildingId) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/rooms?building_id=${adminBuildingId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setRooms(data.data);
        }
      }
    } catch (error) {
      console.error('获取房间列表错误:', error);
    }
  };

  useEffect(() => {
    if (currentUser) {
      fetchBuildingInfo();
    }
  }, [currentUser]);

  useEffect(() => {
    if (adminBuildingId) {
      fetchRooms();
      setIsLoading(false);
    }
  }, [adminBuildingId]);

  if (!currentUser || currentUser.role !== '宿舍管理员') {
    return <p>权限不足。</p>;
  }

  if (isLoading) {
    return (
      <Card title="房间管理">
        <div className="flex justify-center items-center py-8">
          <div className="text-gray-500">加载中...</div>
        </div>
      </Card>
    );
  }

  const buildingName = currentUser.dormBuilding || "未知楼栋";

  const roomColumns = [
    { header: '房间号', accessor: 'room_number' as keyof any },
    { header: '楼层', accessor: 'floor' as keyof any },
    { header: '类型', accessor: 'type' as keyof any },
    { header: '容量', accessor: 'capacity' as keyof any },
    { header: '已住人数', accessor: 'occupied_beds' as keyof any },
    {
      header: '操作',
      accessor: 'id' as keyof any,
      render: (room: any) => (
        <Button size="sm" variant="ghost" onClick={() => handleViewRoomDetails(room)}>
          查看床位
        </Button>
      ),
    },
  ];

  const bedColumns = [
    { header: '床位号', accessor: 'bed_number' as keyof any },
    { header: '状态', accessor: 'status' as keyof any},
    { header: '学生姓名', accessor: (bed: any) => bed.student_name || '空闲' },
  ];
  
  const handleOpenAddRoomModal = () => {
    const defaultType = RoomType.QUAD;
    setNewRoom({ 
        dormBuildingId: adminBuildingId, 
        type: defaultType, 
        capacity: getDefaultCapacity(defaultType), 
        occupiedBeds: 0,
        floor: 1, // Default floor
    });
    setIsAddRoomModalOpen(true);
  };

  const handleCloseAddRoomModal = () => {
    setIsAddRoomModalOpen(false);
    const defaultType = RoomType.QUAD;
    setNewRoom({ type: defaultType, capacity: getDefaultCapacity(defaultType), occupiedBeds: 0 });
  };

  const handleAddRoomInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (name === "type") {
        const roomType = value as RoomType;
        setNewRoom(prev => ({ ...prev, type: roomType, capacity: getDefaultCapacity(roomType) }));
    } else {
        setNewRoom(prev => ({ ...prev, [name]: name === 'floor' || name === 'capacity' || name === 'occupiedBeds' ? parseInt(value) : value }));
    }
  };

  const handleAddRoomSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newRoom.roomNumber && newRoom.floor && newRoom.capacity && adminBuildingId) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_BASE_URL}/rooms`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            room_number: newRoom.roomNumber,
            dorm_building_id: adminBuildingId,
            floor: newRoom.floor,
            type: newRoom.type || RoomType.QUAD,
            capacity: newRoom.capacity,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            // 重新获取房间列表
            await fetchRooms();
            handleCloseAddRoomModal();
            alert('房间添加成功！');
          } else {
            alert(data.message || '添加房间失败');
          }
        } else {
          const errorData = await response.json();
          alert(errorData.message || '添加房间失败');
        }
      } catch (error) {
        console.error('添加房间错误:', error);
        alert('添加房间失败，请重试');
      }
    }
  };


  const handleViewRoomDetails = async (room: any) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/rooms/${room.id}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setViewingRoom(data.data.room);
          setBeds(data.data.beds);
          setIsModalOpen(true);
        }
      }
    } catch (error) {
      console.error('获取房间详情错误:', error);
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setViewingRoom(null);
    setBeds([]);
  };

  return (
    <Card title={`${buildingName} - 房间管理`} actions={
        <Button onClick={handleOpenAddRoomModal} leftIcon={<i className="fas fa-plus mr-2"></i>}>添加房间</Button>
    }>
      <Table columns={roomColumns} data={rooms} keyExtractor={room => room.id} />

      {viewingRoom && (
        <Modal isOpen={isModalOpen} onClose={handleCloseModal} title={`房间 ${viewingRoom.room_number} - 床位详情`} size="lg">
          <Table columns={bedColumns} data={beds} keyExtractor={bed => bed.id} emptyStateMessage="此房间暂无床位信息。" />
          <div className="mt-4 flex justify-end">
            <Button onClick={handleCloseModal}>关闭</Button>
          </div>
        </Modal>
      )}
      
      <Modal isOpen={isAddRoomModalOpen} onClose={handleCloseAddRoomModal} title={`为 ${buildingName} 添加新房间`}>
        <form onSubmit={handleAddRoomSubmit} className="space-y-4">
          <Input name="roomNumber" label="房间号" value={newRoom.roomNumber || ''} onChange={handleAddRoomInputChange} required />
          <Input name="floor" type="number" label="楼层" value={newRoom.floor || ''} onChange={handleAddRoomInputChange} required min="1" />
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">房间类型</label>
            <select
              id="type"
              name="type"
              value={newRoom.type}
              onChange={handleAddRoomInputChange}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              required
            >
              {Object.values(RoomType).map(type => <option key={type} value={type}>{type}</option>)}
            </select>
          </div>
          <Input name="capacity" type="number" label="容量" value={newRoom.capacity || ''} onChange={handleAddRoomInputChange} required min="1" />

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="ghost" onClick={handleCloseAddRoomModal}>取消</Button>
            <Button type="submit">添加房间</Button>
          </div>
        </form>
      </Modal>
    </Card>
  );
};

export default RoomManagementPage;